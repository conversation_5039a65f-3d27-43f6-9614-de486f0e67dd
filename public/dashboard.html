<!DOCTYPE html>
<html lang="uk">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Дашборд - Панель керування</title>
    <link rel="stylesheet" href="/baby-moderator/css/style.css">
    <meta name="description" content="Панель керування моніторингом користувачів">
</head>

<body>
    <div class="app-wrapper">
        <div class="header-block">
            <h1>SoftPhone <span style="color: #2196f3a3;">Pro Max Ultra 512Gb</span></h1>
        </div>

        <div class="dashboard-layout">
            <div class="stats-block">
                <h2>Це наші топчики😋</h2>
                <div class="tabs">
                    <div class="date-selector">
                        <div class="tab active" data-tab="today">Сьогодні</div>
                        <button id="calendarBtn" class="calendar-btn">
                            <svg width="16" height="16" viewBox="0 0 16 16">
                                <path
                                    d="M14 2h-1V0h-2v2H5V0H3v2H2C.9 2 0 2.9 0 4v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H2V7h12v7zM2 4v2h12V4H2z" />
                            </svg>
                        </button>
                        <input type="date" id="dateSelector" style="display: none;">
                    </div>
                    <div class="tab" data-tab="all-time">Весь час</div>
                </div>
                <div class="stats-content" id="allTimeStats"></div>
                <div class="stats-content active" id="todayStats"></div>
            </div>

            <div class="main-block">
                <div class="container">
                    <h1>Панель керування</h1>

                    <fieldset class="form-group">
                      <legend>Статус моніторингу</legend>
                      <div class="checkbox-group" role="group" aria-labelledby="monitoring-status">
                        <label for="startShift">
                          <input type="checkbox" id="startShift" name="monitoring-options">
                          <span>Початок зміни</span>
                            </label>
                            <label for="endShift">
                              <input type="checkbox" id="endShift" name="monitoring-options">
                              <span>Кінець зміни</span>
                            </label>
                            <label for="loadEnabled">
                              <input type="checkbox" id="loadEnabled" name="monitoring-options">
                              <span>Моніторити навантаження</span>
                            </label>
                            <label for="batchMode">
                              <input type="checkbox" id="batchMode" name="monitoring-options">
                              <span>Режим оптимізації</span>
                            </label>
                        </div>
                    </fieldset>

                    <fieldset class="form-group">
                      <legend>Інтервал моніторингу в разі навантаження</legend>
                        <div class="time-range">
                            <label for="timeFrom">
                                Початок інтервалу
                                <input type="time" id="timeFrom" name="timeFrom" value="14:00" aria-describedby="time-help">
                            </label>
                            <label for="timeTo">
                                Кінець інтервалу
                                <input type="time" id="timeTo" name="timeTo" value="16:00" aria-describedby="time-help">
                            </label>
                        </div>
                        <small id="time-help" class="help-text">Встановіть часовий інтервал для моніторингу навантаження</small>
                        </fieldset>

                    <div class="form-group">
                        <label for="targetAudience">Список тих, хто балується🤐</label>
                        <textarea id="targetAudience" name="targetAudience"
                          placeholder="Введіть імена користувачів, розділені крапкою з комою (;)" aria-describedby="audience-help"></textarea>
                        <small id="audience-help" class="help-text">Введіть імена користувачів для моніторингу, розділені крапкою з
                          комою</small>
                    </div>

                    <button onclick="logout()">Вийти з акаунту</button>
                </div>
            </div>
        </div>

    </div>
    <script src="/baby-moderator/js/auth.js"></script>
    <script src="/baby-moderator/js/dashboard.js"></script>
</body>

</html>