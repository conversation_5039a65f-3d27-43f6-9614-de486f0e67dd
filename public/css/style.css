:root {
    --primary-blue: #1e88e5;
    --light-blue: #e3f2fd;
    --white: #ffffff;
    --gray: #f5f5f5;
    --border-color: #ddd;
    --text-color: #333;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 10px;
    --border-radius-small: 5px;
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--gray);
    line-height: 1.6;
    color: var(--text-color);
}

.container {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 30px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    min-height: 720px;
    overflow: hidden;
}

h1 {
    color: var(--primary-blue);
    margin-bottom: 30px;
    text-align: center;
    margin-top: 5px;
    font-size: clamp(1.5rem, 4vw, 2rem);
    word-wrap: break-word;
    hyphens: auto;
}

.form-group {
    margin-bottom: 24px;
}

input[type="text"],
input[type="password"],
input[type="time"],
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    font-size: 14px;
    transition: var(--transition);
    font-family: inherit;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="time"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    background-color: var(--light-blue);
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    background-color: var(--light-blue);
    padding: 20px;
    border-radius: var(--border-radius-small);
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    cursor: pointer;
    word-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    flex-shrink: 0;
}

.time-range {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background-color: var(--light-blue);
    padding: 20px;
    border-radius: var(--border-radius-small);
}

.time-range label {
    margin: 0;
    font-size: 14px;
}

button {
    width: 100%;
    padding: 12px 24px;
    background-color: var(--primary-blue);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    font-family: inherit;
    min-height: 48px;
}

button:hover {
    background-color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
}

button:active {
    transform: translateY(0);
}

button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.3);
}

textarea {
    min-height: 300px;
    width: 100%;
    resize: vertical;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

label {
    display: block;
    color: var(--text-color);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
    word-wrap: break-word;
    hyphens: auto;
}

/* Fieldset and legend styles */
fieldset {
    border: none;
    padding: 0;
    margin: 0;
    margin-bottom: 24px;
}

legend {
    display: block;
    color: var(--text-color);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 14px;
    word-wrap: break-word;
    hyphens: auto;
    padding: 0;
}

/* Help text styles */
.help-text {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    line-height: 1.4;
    font-style: italic;
}

/* Checkbox label improvements */
.checkbox-group label span {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
.stats-container {
    background-color: var(--light-blue);
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #ddd;
}
.error-message {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 30px;
    background-color: var(--white);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

.header-block {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
}

.header-block h1 {
    margin: 0;
    color: var(--primary-blue);
    font-size: clamp(1.5rem, 4vw, 2rem);
    word-wrap: break-word;
    hyphens: auto;
}

.dashboard-layout {
    display: grid;
    grid-template-columns: minmax(300px, 1fr) minmax(400px, 2fr);
    gap: 20px;
    margin: 0;
    padding: 0;
    align-items: start;
}

.stats-block {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    margin: 0;
    max-height: 720px;
    overflow: hidden;
}

.main-block {
    min-width: 0; /* Allows flex item to shrink below content size */
}

.tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
    width: 100%;
}

.tab {
    flex: 1;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    text-align: center;
}

.tab.active {
    border-bottom: 2px solid #007bff;
    color: #007bff;
}

.stats-content {
    display: none;
    overflow-y: auto;
    max-height: 600px;
    padding-right: 12px; /* Додаємо відступ справа для уникнення перекриття тексту */
    box-sizing: border-box; /* Враховуємо padding у розмірах */
}


.stats-content.active {
    display: block;
}

.stats-content::-webkit-scrollbar {
    width: 8px;
}

.stats-content::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
}

.stats-content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

.stats-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.date-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-btn svg {
    fill: #2196f3;
}

.calendar-btn:hover svg {
    fill: #1976d2;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .app-wrapper {
        max-width: 100%;
        padding: 15px;
    }

    .dashboard-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stats-block {
        max-height: none;
        order: 2;
    }

    .main-block {
        order: 1;
    }

    .container {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .app-wrapper {
        padding: 10px;
    }

    .header-block {
        padding: 15px;
        margin-bottom: 15px;
    }

    .container {
        padding: 15px;
        min-height: auto;
    }

    .stats-block {
        padding: 20px;
    }

    .checkbox-group {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 15px;
    }

    .time-range {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab {
        padding: 8px 15px;
        text-align: center;
    }

    .date-selector {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .app-wrapper {
        padding: 5px;
    }

    .header-block,
    .container,
    .stats-block {
        padding: 10px;
        border-radius: var(--border-radius-small);
    }

    h1 {
        font-size: 1.25rem;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 16px;
    }

    input[type="text"],
    input[type="password"],
    input[type="time"],
    textarea {
        padding: 10px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    button {
        padding: 12px 16px;
        font-size: 16px;
    }

    textarea {
        min-height: 200px;
        font-size: 14px;
    }

    .checkbox-group label {
        font-size: 13px;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-color: #000;
    }

    input[type="text"],
    input[type="password"],
    input[type="time"],
    textarea {
        border-width: 2px;
    }
}

/* Print styles */
@media print {
    .app-wrapper {
        max-width: none;
        padding: 0;
    }

    .dashboard-layout {
        display: block;
    }

    .stats-block {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }

    button {
        display: none;
    }
}
